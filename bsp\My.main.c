#include "My.main.h"
#include "stm32f1xx_hal_tim.h"
void Motor_Init()
{
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_1);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_2);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_3);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_4);
}

void Motor_Stop()
{
	// 停止所有电机PWM
	TIM2->CCR1 = 0;
	TIM2->CCR2 = 0;
	TIM2->CCR3 = 0;
	TIM2->CCR4 = 0;
}

void Motor_forword(uint16_t speed_l, uint16_t speed_r)
{
	// 前进：通过PWM电压差控制方向
	// 左电机前进：CH1高，CH2低
	// 右电机前进：CH3高，CH4低
	TIM2->CCR1 = speed_r;  // 左电机正向PWM
	TIM2->CCR2 = 0;        // 左电机反向PWM = 0
	TIM2->CCR4 = speed_l;  // 右电机正向PWM
	TIM2->CCR3 = 0;        // 右电机反向PWM = 0
}

void Motor_back(uint16_t speed_l, uint16_t speed_r)
{
	// 后退：通过PWM电压差控制方向（与前进相反）
	// 左电机后退：CH1低，CH2高
	// 右电机后退：CH3低，CH4高
	TIM2->CCR1 = 0;        // 左电机正向PWM = 0
	TIM2->CCR2 = speed_r;  // 左电机反向PWM
	TIM2->CCR4 = 0;        // 右电机正向PWM = 0
	TIM2->CCR3 = speed_l;  // 右电机反向PWM
}

void task()
{
		adc_task();
		Motor_forword(230 -PID_Init(),260+PID_Init());


}

void setup(void)
{
	Motor_Init();
	adc_tim_dma_init();  
	// 初始化OLED显示	
	HAL_TIM_Base_Start_IT(&htim3);
	OLED_Init();
	OLED_Clear();
	OLED_ShowString(1, 1, "System Init...");
	HAL_Delay(1000);

	// 启动时先停止电机，等待稳定
	Motor_Stop();
	HAL_Delay(1000);  // 启动延时1秒

}

void loop(void)
{	
	task();
	
		OLED_ShowNum(1, 1,300-PID_Init() , 4);
		// 显示传感器电压值（毫伏）
		OLED_ShowString(1, 1, "V:");
		OLED_ShowNum(1, 3, (uint32_t)(voltage_IN4 * 1000), 4);  // 右传感器
		OLED_ShowNum(1, 8, (uint32_t)(voltage_IN5 * 1000), 4);  // 中传感器
		OLED_ShowNum(1, 13, (uint32_t)(voltage_IN6 * 1000), 4); // 左传感器

		// 显示线宽信息
		OLED_ShowString(2, 1, "W:");
		OLED_ShowNum(2, 3, Get_Line_Width_Type(), 1);           // 线宽类型
		OLED_ShowString(2, 5, "S:");
		OLED_ShowNum(2, 7, Get_Sensors_On_Line_Count(), 1);     // 传感器数量
		OLED_ShowString(2, 9, "M:");
		OLED_ShowNum(2, 11, (uint32_t)Get_Line_Width_MM(), 2);  // 线宽毫米

		// 显示传感器状态（0=白色，1=黑色）
		uint8_t left_status, center_status, right_status;
		Get_Sensor_Status(&left_status, &center_status, &right_status);
		OLED_ShowString(3, 1, "S:");
		OLED_ShowNum(3, 3, left_status, 1);    // 左传感器状态
		OLED_ShowNum(3, 5, center_status, 1);  // 中传感器状态
		OLED_ShowNum(3, 7, right_status, 1);   // 右传感器状态

		// 显示PID输出
		OLED_ShowString(3, 9, "P:");
		OLED_ShowSignedNum(3, 11, PID_Init(), 4);

		// 显示误差值
		OLED_ShowString(4, 1, "E:");
		OLED_ShowSignedNum(4, 3, (int16_t)(error * 10), 4);
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
		PID_Init();
	
}
