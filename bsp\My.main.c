#include "My.main.h"
int16_t speed_l = 500;
int16_t speed_r = 500;

void Motor_Init()
{
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_1);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_2);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_3);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_4);

	// 初始化方向控制GPIO
	GPIO_InitTypeDef GPIO_InitStruct = {0};

	// 使能GPIOB时钟
	__HAL_RCC_GPIOB_CLK_ENABLE();

	// 配置PB12-PB15为输出模式 (电机方向控制)
	GPIO_InitStruct.Pin = GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_NOPULL;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
	HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

	// 初始状态：所有方向控制引脚为低
	HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15, GPIO_PIN_RESET);
}

void Motor_Stop()
{
	// 停止所有电机PWM
	TIM2->CCR1 = 0;
	TIM2->CCR2 = 0;
	TIM2->CCR3 = 0;
	TIM2->CCR4 = 0;

	// 所有方向控制引脚设为低电平
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,GPIO_PIN_RESET);
}

void Motor_forword(uint16_t speed_l, uint16_t speed_r)
{
	// 先停止电机
	Motor_Stop();
	HAL_Delay(10);

	// 设置前进方向：左电机正转，右电机正转
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,GPIO_PIN_SET);   // 左电机 IN1 = 1
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,GPIO_PIN_RESET); // 左电机 IN2 = 0
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,GPIO_PIN_SET);   // 右电机 IN3 = 1
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,GPIO_PIN_RESET); // 右电机 IN4 = 0

	// 设置PWM速度
	TIM2->CCR2 = speed_l;  // 左电机速度
	TIM2->CCR3 = speed_r;  // 右电机速度
}

void Motor_back(uint16_t speed_l, uint16_t speed_r)
{
	// 先停止电机
	Motor_Stop();
	HAL_Delay(10);

	// 设置后退方向：左电机反转，右电机反转
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,GPIO_PIN_RESET); // 左电机 IN1 = 0
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,GPIO_PIN_SET);   // 左电机 IN2 = 1
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,GPIO_PIN_RESET); // 右电机 IN3 = 0
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,GPIO_PIN_SET);   // 右电机 IN4 = 1

	// 设置PWM速度
	TIM2->CCR2 = speed_l;  // 左电机速度
	TIM2->CCR3 = speed_r;  // 右电机速度
}

// 保持原有的单独控制函数以兼容现有代码
void Motor_forword_l(int16_t speed)
{
	TIM2->CCR2 = speed;
}
void Motor_forword_R(int16_t speed)
{
	TIM2->CCR3 = speed;
}

void setup(void)
{
	Motor_Init(); 
	adc_tim_dma_init();
}

void loop(void)
{
	// 选择运行模式：0=循迹模式，1=测试模式
	#define TEST_MODE 1

	#if TEST_MODE == 1
		// 测试模式：测试前进和后退功能
		Motor_forword(800, 800);  // 前进
		HAL_Delay(1000);

		Motor_Stop();
		HAL_Delay(100);

		Motor_back(800, 800);     // 后退
		HAL_Delay(1000);

		Motor_Stop();
		HAL_Delay(500);

	#else
		// 原有的循迹模式
		PID_Init();
		speed_l += PID_Init();
		speed_r -= PID_Init();
			if(speed_l>=1000)
			{
				speed_l = 1000;
			}else if(speed_l<0)
			{
				speed_l = 0;
			}
			if(speed_r>=1000)
			{
				speed_r = 1000;
			}else if(speed_r<0)
			{
				speed_r = 0;
			}
		Motor_forword_l(speed_l);
		Motor_forword_R(speed_r);
	#endif
}
