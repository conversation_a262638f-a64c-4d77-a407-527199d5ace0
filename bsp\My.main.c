#include "My.main.h"
int16_t speed_l = 500;
int16_t speed_r = 500;

void Motor_Init()
{
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_1);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_2);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_3);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_4);


}

void Motor_Stop()
{
	// 停止所有电机PWM
	TIM2->CCR1 = 0;
	TIM2->CCR2 = 0;
	TIM2->CCR3 = 0;
	TIM2->CCR4 = 0;
}

void Motor_forword(uint16_t speed_l, uint16_t speed_r)
{
	// 前进：通过PWM电压差控制方向
	// 左电机前进：CH1高，CH2低
	// 右电机前进：CH3高，CH4低
	TIM2->CCR1 = speed_l;  // 左电机正向PWM
	TIM2->CCR2 = 0;        // 左电机反向PWM = 0
	TIM2->CCR4 = speed_r;  // 右电机正向PWM
	TIM2->CCR3 = 0;        // 右电机反向PWM = 0
}

void Motor_back(uint16_t speed_l, uint16_t speed_r)
{
	// 后退：通过PWM电压差控制方向（与前进相反）
	// 左电机后退：CH1低，CH2高
	// 右电机后退：CH3低，CH4高
	TIM2->CCR1 = 0;        // 左电机正向PWM = 0
	TIM2->CCR2 = speed_l;  // 左电机反向PWM
	TIM2->CCR4 = 0;        // 右电机正向PWM = 0
	TIM2->CCR3 = speed_r;  // 右电机反向PWM
}

// 保持原有的单独控制函数以兼容现有代码
void Motor_forword_l(int16_t speed)
{
	// 左电机前进：CH1高，CH2低
	TIM2->CCR1 = speed;
	TIM2->CCR2 = 0;
}
void Motor_forword_R(int16_t speed)
{
	// 右电机前进：CH3高，CH4低
	TIM2->CCR3 = speed;
	TIM2->CCR4 = 0;
}

void setup(void)
{
	Motor_Init();
	adc_tim_dma_init();

	// 启动时先停止电机，等待稳定
	Motor_Stop();
	HAL_Delay(1000);  // 启动延时1秒

	// 可选：重置线宽标定状态（如果需要重新标定）
	// Reset_LineWidth_Calibration();
}

void loop(void)
{
	// 选择运行模式：0=手动标定循迹模式，1=测试模式，2=静态标定模式，3=自动标定模式，5=直接循迹模式
	#define TEST_MODE 5

	#if TEST_MODE == 4
		// 测试模式：测试前进和后退功能

		// 确保开始时停止
		Motor_Stop();
		HAL_Delay(500);

		// 前进测试
		Motor_forword(600, 600);  // 前进（降低速度更安全）
		HAL_Delay(1500);

		// 停止
		Motor_Stop();
		HAL_Delay(500);

		// 后退测试
		Motor_back(600, 600);     // 后退
		HAL_Delay(1500);

		// 停止
		Motor_Stop();
		HAL_Delay(1000);

	#elif TEST_MODE == 2
		// 静态线宽标定模式：小车完全静止，手动移动进行标定
		Motor_Stop();  // 始终保持静止
		// 在这个模式下，您需要手动移动小车来识别线宽
		// 可以通过调试器观察 state_1, state_2, line_width_calibrated 的状态

	#elif TEST_MODE == 3
		// 自动线宽标定模式：小车自动前进进行标定
		if(line_width_calibrated == 0)
		{
			// 线宽标定阶段：小车缓慢前进进行线宽识别
			Motor_forword(300, 300);  // 缓慢前进，便于识别线宽
		}
		else
		{
			// 标定完成，开始循迹
			PID_Init();
			speed_l += PID_Init();
			speed_r -= PID_Init();

			// 速度限制
			if(speed_l>=1000) speed_l = 1000;
			else if(speed_l<0) speed_l = 0;
			if(speed_r>=1000) speed_r = 1000;
			else if(speed_r<0) speed_r = 0;

			Motor_forword_l(speed_l);
			Motor_forword_R(speed_r);
		}

	#elif TEST_MODE == 5
		// 直接循迹模式：跳过线宽识别，直接开始循迹
		int16_t pid_output = PID_Init();  // 获取PID输出

		// 基础速度 + PID调节
		int16_t base_speed = 400;  // 降低基础速度便于调试
		speed_l = base_speed + pid_output;  // 左轮速度
		speed_r = base_speed - pid_output;  // 右轮速度

		// 速度限制
		if(speed_l >= 800) speed_l = 800;
		else if(speed_l < 0) speed_l = 0;
		if(speed_r >= 800) speed_r = 800;
		else if(speed_r < 0) speed_r = 0;

		// 使用改进的电机控制函数
		Motor_forword(speed_l, speed_r);

	#else
		// 正常循迹模式：手动移动小车进行线宽识别，然后开始循迹
		if(system_ready == 0)
		{
			// 线宽识别阶段：小车静止，等待手动移动进行线宽识别
			Motor_Stop();  // 小车静止，由用户手动移动

			// 可以添加LED指示或其他提示表示正在识别线宽
			// 例如：闪烁LED表示等待识别
		}
		else
		{
			// 线宽识别完成，开始正常循迹
			int16_t pid_output = PID_Init();  // 获取PID输出

			// 基础速度 + PID调节
			int16_t base_speed = 500;  // 基础前进速度
			speed_l = base_speed + pid_output;  // 左轮速度
			speed_r = base_speed - pid_output;  // 右轮速度

			// 速度限制
			if(speed_l >= 1000)
			{
				speed_l = 1000;
			}else if(speed_l < 0)
			{
				speed_l = 0;
			}
			if(speed_r >= 1000)
			{
				speed_r = 1000;
			}else if(speed_r < 0)
			{
				speed_r = 0;
			}

			// 使用改进的电机控制函数
			Motor_forword(speed_l, speed_r);
		}
	#endif
}
