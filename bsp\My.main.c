#include "My.main.h"
extern float voltage_IN6;
extern float voltage_IN5;
extern float voltage_IN4;
extern uint16_t ADC_IN4_avg;
extern uint16_t ADC_IN5_avg;
extern uint16_t ADC_IN6_avg;

void Motor_Init()
{
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_1);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_2);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_3);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_4);


}

void Motor_Stop()
{
	// 停止所有电机PWM
	TIM2->CCR1 = 0;
	TIM2->CCR2 = 0;
	TIM2->CCR3 = 0;
	TIM2->CCR4 = 0;
}

void Motor_forword(uint16_t speed_l, uint16_t speed_r)
{
	// 前进：通过PWM电压差控制方向
	// 左电机前进：CH1高，CH2低
	// 右电机前进：CH3高，CH4低
	TIM2->CCR1 = speed_r;  // 左电机正向PWM
	TIM2->CCR2 = 0;        // 左电机反向PWM = 0
	TIM2->CCR4 = speed_l;  // 右电机正向PWM
	TIM2->CCR3 = 0;        // 右电机反向PWM = 0
}

void Motor_back(uint16_t speed_l, uint16_t speed_r)
{
	// 后退：通过PWM电压差控制方向（与前进相反）
	// 左电机后退：CH1低，CH2高
	// 右电机后退：CH3低，CH4高
	TIM2->CCR1 = 0;        // 左电机正向PWM = 0
	TIM2->CCR2 = speed_r;  // 左电机反向PWM
	TIM2->CCR4 = 0;        // 右电机正向PWM = 0
	TIM2->CCR3 = speed_l;  // 右电机反向PWM
}

// 保持原有的单独控制函数以兼容现有代码
//void Motor_forword_l(int16_t speed)
//{
//	// 左电机前进：CH1高，CH2低
//	TIM2->CCR1 = speed;
//	TIM2->CCR2 = 0;
//}
//void Motor_forword_R(int16_t speed)
//{
//	// 右电机前进：CH3高，CH4低
//	TIM2->CCR3 = speed;
//	TIM2->CCR4 = 0;
//}

void setup(void)
{
	Motor_Init();
	adc_tim_dma_init();

	// 初始化OLED显示
	OLED_Init();
	OLED_Clear();
	OLED_ShowString(1, 1, "System Init...");
	HAL_Delay(1000);

	// 启动时先停止电机，等待稳定
	Motor_Stop();
	HAL_Delay(1000);  // 启动延时1秒


	// 可选：重置线宽标定状态（如果需要重新标定）
	// Reset_LineWidth_Calibration();
}

void loop(void)
{
	adc_task();
	GetTrace_Date();
	// 选择运行模式：0=手动标定循迹模式，1=测试模式，2=静态标定模式，3=自动标定模式，5=直接循迹模式

//			// 1000次后使用真实PID计算
//			int16_t pid_output = PID_Init();  // 获取PID输出

//			// 基础速度 + PID调节
//			int16_t base_speed = 400;  // 降低基础速度便于调试
//			speed_l = base_speed + pid_output;  // 左轮速度
//			speed_r = 460 - pid_output;  // 右轮速度

//			// 速度限制
//			if(speed_l >= 800) speed_l = 800;
//			else if(speed_l < 0) speed_l = 0;
//			if(speed_r >= 800) speed_r = 800;
//			else if(speed_r < 0) speed_r = 0;

//		// 使用改进的电机控制函数
//		Motor_forword(speed_l, speed_r);

		// 每30次循环更新一次OLED显示（提高刷新频率便于调试）

		OLED_ShowString(1, 1, "Sensor:");
		// 显示传感器数据（电压值和ADC原始值）
		OLED_ShowString(1, 1, "ADC Sensors:");

		// 第2行：显示电压值（单位：V，保留2位小数）
		OLED_ShowString(2, 1, "V:");
		OLED_ShowFloat(2, 3, voltage_IN4, 1, 2);   // IN4电压值，如1.65
		OLED_ShowFloat(2, 7, voltage_IN5, 1, 2);   // IN5电压值
		OLED_ShowFloat(2, 11, voltage_IN6, 1, 2);  // IN6电压值

		// 第3行：显示ADC原始值（0-4095）
		OLED_ShowString(3, 1, "ADC:");
		OLED_ShowNum(3, 5, (uint32_t)(voltage_IN4 * 4096 / 3.3), 4);   // 反推ADC值
		OLED_ShowNum(3, 10, (uint32_t)(voltage_IN5 * 4096 / 3.3), 4);  // 反推ADC值

		// 第4行：显示第三个传感器的ADC值和误差
		OLED_ShowNum(4, 1, (uint32_t)(voltage_IN6 * 4096 / 3.3), 4);   // IN6的ADC值
		OLED_ShowString(4, 6, "E:");
		OLED_ShowSignedNum(4, 8, (int16_t)(error * 10), 4);            // 误差值×10

}
