#include "My.main.h"
int16_t speed_l = 500;
int16_t speed_r = 500;

void Motor_Init()
{
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_1);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_2);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_3);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_4);

	// 初始化方向控制GPIO
	GPIO_InitTypeDef GPIO_InitStruct = {0};

	// 使能GPIOB时钟
	__HAL_RCC_GPIOB_CLK_ENABLE();

	// 配置PB12-PB15为输出模式 (电机方向控制)
	GPIO_InitStruct.Pin = GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_NOPULL;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
	HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

	// 初始状态：所有方向控制引脚为低
	HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15, GPIO_PIN_RESET);
}

void Motor_Stop()
{
	// 停止所有电机PWM
	TIM2->CCR1 = 0;
	TIM2->CCR2 = 0;
	TIM2->CCR3 = 0;
	TIM2->CCR4 = 0;

	// 所有方向控制引脚设为低电平
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,GPIO_PIN_RESET);
}

void Motor_forword(uint16_t speed_l, uint16_t speed_r)
{
	// 先停止电机
	Motor_Stop();
	HAL_Delay(10);

	// 设置前进方向：左电机正转，右电机正转
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,GPIO_PIN_SET);   // 左电机 IN1 = 1
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,GPIO_PIN_RESET); // 左电机 IN2 = 0
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,GPIO_PIN_SET);   // 右电机 IN3 = 1
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,GPIO_PIN_RESET); // 右电机 IN4 = 0

	// 设置PWM速度
	TIM2->CCR1 = speed_l;  // 左电机速度
	TIM2->CCR4 = speed_r;  // 右电机速度
}

void Motor_back(uint16_t speed_l, uint16_t speed_r)
{
	// 先停止电机
	Motor_Stop();
	HAL_Delay(10);

	// 设置后退方向：左电机反转，右电机反转
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,GPIO_PIN_RESET); // 左电机 IN1 = 0
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,GPIO_PIN_SET);   // 左电机 IN2 = 1
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,GPIO_PIN_RESET); // 右电机 IN3 = 0
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,GPIO_PIN_SET);   // 右电机 IN4 = 1

	// 设置PWM速度
	TIM2->CCR1 = speed_l;  // 左电机速度
	TIM2->CCR4 = speed_r;  // 右电机速度
}

// 保持原有的单独控制函数以兼容现有代码
void Motor_forword_l(int16_t speed)
{
	TIM2->CCR1 = speed;
}
void Motor_forword_R(int16_t speed)
{
	TIM2->CCR4 = speed;
}

void setup(void)
{
	Motor_Init();
	adc_tim_dma_init();

	// 可选：重置线宽标定状态（如果需要重新标定）
	// Reset_LineWidth_Calibration();
}

void loop(void)
{
	// 选择运行模式：0=手动标定循迹模式，1=测试模式，2=静态标定模式，3=自动标定模式
	#define TEST_MODE 0

	#if TEST_MODE == 1
		// 测试模式：测试前进和后退功能
		Motor_forword(800, 800);  // 前进
		HAL_Delay(1000);

		Motor_Stop();
		HAL_Delay(100);

		Motor_back(800, 800);     // 后退
		HAL_Delay(1000);

		Motor_Stop();
		HAL_Delay(500);

	#elif TEST_MODE == 2
		// 静态线宽标定模式：小车完全静止，手动移动进行标定
		Motor_Stop();  // 始终保持静止
		// 在这个模式下，您需要手动移动小车来识别线宽
		// 可以通过调试器观察 state_1, state_2, line_width_calibrated 的状态

	#elif TEST_MODE == 3
		// 自动线宽标定模式：小车自动前进进行标定
		if(line_width_calibrated == 0)
		{
			// 线宽标定阶段：小车缓慢前进进行线宽识别
			Motor_forword(300, 300);  // 缓慢前进，便于识别线宽
		}
		else
		{
			// 标定完成，开始循迹
			PID_Init();
			speed_l += PID_Init();
			speed_r -= PID_Init();

			// 速度限制
			if(speed_l>=1000) speed_l = 1000;
			else if(speed_l<0) speed_l = 0;
			if(speed_r>=1000) speed_r = 1000;
			else if(speed_r<0) speed_r = 0;

			Motor_forword_l(speed_l);
			Motor_forword_R(speed_r);
		}

	#else
		// 正常循迹模式：手动移动小车进行线宽识别，然后开始循迹
		if(system_ready == 0)
		{
			// 线宽识别阶段：小车静止，等待手动移动进行线宽识别
			Motor_Stop();  // 小车静止，由用户手动移动

			// 可以添加LED指示或其他提示表示正在识别线宽
			// 例如：闪烁LED表示等待识别
		}
		else
		{
			// 线宽识别完成，开始正常循迹
			PID_Init();
			speed_l += PID_Init();
			speed_r -= PID_Init();

			// 速度限制
			if(speed_l>=1000)
			{
				speed_l = 1000;
			}else if(speed_l<0)
			{
				speed_l = 0;
			}
			if(speed_r>=1000)
			{
				speed_r = 1000;
			}else if(speed_r<0)
			{
				speed_r = 0;
			}
			Motor_forword_l(speed_l);
			Motor_forword_R(speed_r);
		}
	#endif
}
