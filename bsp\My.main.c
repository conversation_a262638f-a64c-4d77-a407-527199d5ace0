#include "My.main.h"
#include "stm32f1xx_hal_tim.h"
void Motor_Init()
{
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_1);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_2);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_3);
	HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_4);
}

void Motor_Stop()
{
	// 停止所有电机PWM
	TIM2->CCR1 = 0;
	TIM2->CCR2 = 0;
	TIM2->CCR3 = 0;
	TIM2->CCR4 = 0;
}

void Motor_forword(uint16_t speed_l, uint16_t speed_r)
{
	// 前进：通过PWM电压差控制方向
	// 左电机前进：CH1高，CH2低
	// 右电机前进：CH3高，CH4低
	TIM2->CCR1 = speed_r;  // 左电机正向PWM
	TIM2->CCR2 = 0;        // 左电机反向PWM = 0
	TIM2->CCR4 = speed_l;  // 右电机正向PWM
	TIM2->CCR3 = 0;        // 右电机反向PWM = 0
}

void Motor_back(uint16_t speed_l, uint16_t speed_r)
{
	// 后退：通过PWM电压差控制方向（与前进相反）
	// 左电机后退：CH1低，CH2高
	// 右电机后退：CH3低，CH4高
	TIM2->CCR1 = 0;        // 左电机正向PWM = 0
	TIM2->CCR2 = speed_r;  // 左电机反向PWM
	TIM2->CCR4 = 0;        // 右电机正向PWM = 0
	TIM2->CCR3 = speed_l;  // 右电机反向PWM
}

void task()
{
		adc_task();
		Motor_forword(230 -PID_Init(),260+PID_Init());


}

void setup(void)
{
	Motor_Init();
	adc_tim_dma_init();  
	// 初始化OLED显示	
	HAL_TIM_Base_Start_IT(&htim3);
	OLED_Init();
	OLED_Clear();
	OLED_ShowString(1, 1, "System Init...");
	HAL_Delay(1000);

	// 启动时先停止电机，等待稳定
	Motor_Stop();
	HAL_Delay(1000);  // 启动延时1秒

}

void loop(void)
{	
	task();
	
		OLED_ShowNum(1, 1,300-PID_Init() , 4);
		// 显示传感器电压值（毫伏）
		OLED_ShowString(1, 1, "V:");
		OLED_ShowNum(1, 3, (uint32_t)(voltage_IN4 * 1000), 4);  // 右传感器
		OLED_ShowNum(1, 8, (uint32_t)(voltage_IN5 * 1000), 4);  // 中传感器
		OLED_ShowNum(1, 13, (uint32_t)(voltage_IN6 * 1000), 4); // 左传感器

		// 显示传感器状态和阈值
		uint8_t left_status, center_status, right_status;
		float left_thresh, right_thresh;
		Get_Sensor_Status_Info(&left_status, &center_status, &right_status, &left_thresh, &right_thresh);

		OLED_ShowString(2, 1, "S:");
		OLED_ShowNum(2, 3, left_status, 1);     // 左传感器状态
		OLED_ShowNum(2, 5, center_status, 1);   // 中传感器状态
		OLED_ShowNum(2, 7, right_status, 1);    // 右传感器状态
		OLED_ShowString(2, 9, "P:");
		OLED_ShowNum(2, 11, Get_Sensor_Pattern(), 1);  // 传感器模式

		// 显示阈值（毫伏）
		OLED_ShowString(3, 1, "T:");
		OLED_ShowNum(3, 3, (uint32_t)(left_thresh * 1000), 4);   // 左阈值
		OLED_ShowNum(3, 8, (uint32_t)(right_thresh * 1000), 4);  // 右阈值

		// 显示PID输出和误差
		OLED_ShowString(4, 1, "PID:");
		OLED_ShowSignedNum(4, 5, PID_Init(), 4);
		OLED_ShowString(4, 10, "E:");
		OLED_ShowSignedNum(4, 12, (int16_t)(error * 10), 4);
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
		PID_Init();
	
}
