#ifndef __ADC_APP_H_
#define __ADC_APP_H_

#include "stdint.h"
#include "mydefine.h"

// 原有函数声明
void Get_DATA(void);
float GetTrace_Date(void);
void adc_tim_dma_init(void);
void adc_task(void);

// 线宽标定相关函数声明
uint8_t Get_LineWidth_Status(void);
void Get_Calibration_Status(uint8_t *left_done, uint8_t *right_done, uint8_t *all_done);
void Get_Sensor_Voltages(float *v4, float *v5, float *v6);
void Get_Thresholds(float *left_thresh, float *right_thresh);
void Reset_LineWidth_Calibration(void);

// 自适应线宽识别相关函数
uint8_t Get_Line_Width_Type(void);
float Get_Adaptive_Threshold(void);
void Get_Calibrated_Levels(float *white, float *black);
void Get_Line_Width_Debug_Info(uint8_t *width_type, float *threshold, float *white, float *black);
void Set_Threshold_Ratio(float ratio);
void Reset_Line_Width_Calibration_New(void);

// 变量声明
extern float voltage_IN4, voltage_IN5, voltage_IN6;
extern float error;
extern uint8_t line_width_calibrated;  // 线宽标定完成标志
extern uint8_t system_ready;           // 系统准备就绪标志

#endif /* __ADC_APP_H_ */
