#ifndef __ADC_APP_H_
#define __ADC_APP_H_

#include "stdint.h"
#include "mydefine.h"

// 原有函数声明
void Get_DATA(void);
float GetTrace_Date(void);
void adc_tim_dma_init(void);
void adc_task(void);

// 线宽标定相关函数声明
uint8_t Get_LineWidth_Status(void);
void Reset_LineWidth_Calibration(void);

// 线宽标定相关变量声明
extern uint8_t line_width_calibrated;  // 线宽标定完成标志
extern uint8_t system_ready;           // 系统准备就绪标志

#endif /* __ADC_APP_H_ */
