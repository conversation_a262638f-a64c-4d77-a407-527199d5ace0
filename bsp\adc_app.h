#ifndef __ADC_APP_H_
#define __ADC_APP_H_

#include "stdint.h"
#include "mydefine.h"

// 原有函数声明
void Get_DATA(void);
float GetTrace_Date(void);
void adc_tim_dma_init(void);
void adc_task(void);

// 线宽标定相关函数声明
uint8_t Get_LineWidth_Status(void);
void Get_Calibration_Status(uint8_t *left_done, uint8_t *right_done, uint8_t *all_done);
void Get_Sensor_Voltages(float *v4, float *v5, float *v6);
void Get_Thresholds(float *left_thresh, float *right_thresh);
void Reset_LineWidth_Calibration(void);
void Set_ADC_Filter_Alpha(float alpha);
void Get_Raw_ADC_Data(float *raw_v4, float *raw_v5, float *raw_v6);

// 变量声明
extern float voltage_IN4, voltage_IN5, voltage_IN6;
extern float voltage_IN4_filtered, voltage_IN5_filtered, voltage_IN6_filtered;  // 滤波后的数据
extern float error;
extern uint8_t line_width_calibrated;  // 线宽标定完成标志
extern uint8_t system_ready;           // 系统准备就绪标志

#endif /* __ADC_APP_H_ */
