#include "my_main.h"

void Motor_Init()
{
	HAL_TIM_PWM_Start(&htim1,TIM_CHANNEL_1);
	HAL_TIM_PWM_Start(&htim1,TIM_CHANNEL_4);
}

void Motor_forword(uint16_t speed)
{
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,GPIO_PIN_SET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,GPIO_PIN_SET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,GPIO_PIN_RESET);
	
	TIM1->CCR1 = speed;
	TIM1->CCR4 = speed;
}

void Motor_back(uint16_t speed)
{
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,GPIO_PIN_SET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,GPIO_PIN_SET);
	
	TIM1->CCR1 = speed;
	TIM1->CCR4 = speed;
}

void Motor_left(uint16_t speed,uint16_t s_speed)
{

	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,GPIO_PIN_SET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,GPIO_PIN_SET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,GPIO_PIN_RESET);

	TIM1->CCR1 = speed;
	TIM1->CCR4 = s_speed;
	
}

void Motor_right(uint16_t speed,uint16_t s_speed)
{

	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,GPIO_PIN_SET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,GPIO_PIN_RESET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,GPIO_PIN_SET);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,GPIO_PIN_RESET);

	TIM1->CCR1 = s_speed;
	TIM1->CCR4 = speed;
	
}
void setup(void)
{
	Motor_Init();
}

void loop(void)
{
	Motor_forword(500);
	HAL_Delay(500);
	Motor_back(500);
	HAL_Delay(500);
	Motor_left(1000,400);
	HAL_Delay(500);
	Motor_right(1000,400);
	HAL_Delay(500);
}
