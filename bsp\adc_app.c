
#include "adc_app.h"
#include "stm32f1xx_hal_tim.h"
#include <math.h>

// 1 ��ѯ
// 2 DMA����ת��
// 3 DMA TIM ��ͨ���ɼ�
#define ADC_MODE (3)

// --- ���� AD<PERSON> ��ش��� ---

#if ADC_MODE == 1

#elif ADC_MODE == 3

#define BUFFER_SIZE 2046  
extern DMA_HandleTypeDef hdma_adc1;
uint32_t ADC_IN4[BUFFER_SIZE / 3];
uint32_t ADC_IN5[BUFFER_SIZE / 3];
uint32_t ADC_IN6[BUFFER_SIZE / 3];

__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;
uint8_t wave_analysis_flag = 0; // ���η�����־λ
uint8_t wave_query_type = 0;    // ���β�ѯ���ͣ�0=ȫ��, 1=����, 2=Ƶ��, 3=���ֵ
float error = 0;

float Data_Out; //�������
float LEFT_THREASH; //����ֵ
float RIGHT_THREASH; //����ֵ
float LEFT_MAX = 3.3; //�����ֵ
float RIGHT_MAX = 3.3; //�����ֵ
float voltage_IN6; //�󴫸������ADֵ
float voltage_IN5;
float voltage_IN4; //�Ҵ��������ADֵ
uint8_t state_1 = 0;
uint8_t state_2 = 0;
uint8_t line_width_calibrated = 0;  // 线宽标定完成标志
uint8_t system_ready = 0;           // 系统准备就绪标志

void Get_DATA()
{
	// 手动移动线宽识别逻辑
	float tolerance = 0.1f;  // 增大容忍度，便于手动识别

	// 左侧线宽识别：当中间传感器与左传感器电压接近时
	if(fabs(voltage_IN5 - voltage_IN6) < tolerance && state_1 == 0)
	{
		LEFT_THREASH = (voltage_IN5 + voltage_IN6) / 2.0f;  // 取平均值更稳定
		state_1 = 1;
		// 可以在这里添加提示音或LED指示
	}

	// 右侧线宽识别：当中间传感器与右传感器电压接近时
	if(fabs(voltage_IN5 - voltage_IN4) < tolerance && state_2 == 0)
	{
		RIGHT_THREASH = (voltage_IN5 + voltage_IN4) / 2.0f;  // 取平均值更稳定
		state_2 = 1;
		// 可以在这里添加提示音或LED指示
	}

	// 当左右阈值都识别完成时，标记线宽标定完成
	if(state_1 == 1 && state_2 == 1 && line_width_calibrated == 0)
	{
		line_width_calibrated = 1;
		system_ready = 1;  // 系统准备就绪，可以开始循迹
		// 可以在这里添加完成提示音或LED指示
	}
}

// 新增：获取线宽标定状态的函数
uint8_t Get_LineWidth_Status(void)
{
	return line_width_calibrated;
}

// 新增：获取详细标定状态的函数
void Get_Calibration_Status(uint8_t *left_done, uint8_t *right_done, uint8_t *all_done)
{
	*left_done = state_1;
	*right_done = state_2;
	*all_done = line_width_calibrated;
}

// 新增：获取当前传感器电压值的函数
void Get_Sensor_Voltages(float *v4, float *v5, float *v6)
{
	*v4 = voltage_IN4;  // 右传感器
	*v5 = voltage_IN5;  // 中间传感器
	*v6 = voltage_IN6;  // 左传感器
}

// 新增：获取阈值的函数
void Get_Thresholds(float *left_thresh, float *right_thresh)
{
	*left_thresh = LEFT_THREASH;
	*right_thresh = RIGHT_THREASH;
}

// 新增：重置线宽标定的函数
void Reset_LineWidth_Calibration(void)
{
	state_1 = 0;
	state_2 = 0;
	line_width_calibrated = 0;
	system_ready = 0;
	LEFT_THREASH = 0;
	RIGHT_THREASH = 0;
}


float GetTrace_Date(void)
{
	// 简化的循迹误差计算，不依赖线宽标定
	Data_Out = (voltage_IN6 - voltage_IN4);  // 左传感器 - 右传感器

	// 如果线宽已标定，使用复杂算法
	if(line_width_calibrated == 1)
	{
		if((Data_Out>0)&&((voltage_IN5<LEFT_THREASH)))
		{
			Data_Out=(2*LEFT_MAX-voltage_IN6)*2;
		}
		else if((Data_Out<0)&&((voltage_IN5<RIGHT_THREASH)))
		{
			Data_Out=(2*RIGHT_MAX-voltage_IN4)*2;
		}
	}
	// 如果未标定，使用简单的差值算法
	else
	{
		// 简单的三传感器循迹算法
		float left_weight = voltage_IN6;   // 左传感器
		float center_weight = voltage_IN5; // 中传感器
		float right_weight = voltage_IN4;  // 右传感器

		// 计算加权误差
		Data_Out = (left_weight - right_weight) * 100;  // 放大误差便于PID控制
	}

	return Data_Out;
}


void adc_tim_dma_init(void)
{
		HAL_TIM_Base_Start_IT(&htim3);
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // ���ð봫���ж�
}

// ��usart_app.c������Ϊextern

void adc_task(void)
{
    // һ������ת�� 3(����) + 12.5(ת��) = 15.5 ADCʱ������
    // ���� ADC ʱ�� 14MHz (���� HSI/PLL), һ��ת��ʱ��: 15.5 / 14MHz ~= 1.1 us
    // BUFFER_SIZE ��ת����ʱ��: 1000 * 1.1 us = 1.1 ms (����ֵ)
    // ��ʱ������Ƶ����Ҫƥ��������ʻ����

    if (AdcConvEnd) // ���ת����ɱ�־
    {
        // --- �����ɼ��������� ---
        // ʾ�������������������ݸ��Ƶ���һ�������� (ԭ��δ֪����ԭ�߼�����)
        for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
        {            
						ADC_IN4[i] = adc_val_buffer[i * 3];
            ADC_IN5[i] = adc_val_buffer[i * 3 + 1]; // �� ADC ���ݴ�����Ϊ dac_val_buffer ������
						ADC_IN6[i] = adc_val_buffer[i * 3 + 2]; 
	
        }
        uint32_t ADC_IN4_sum = 0;
				uint32_t ADC_IN5_sum = 0;
				uint32_t ADC_IN6_sum = 0;
        // �� res_val_buffer �е�����ת��Ϊ��ѹֵ
        for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
        {
            ADC_IN4_sum += ADC_IN4[i];
						ADC_IN5_sum += ADC_IN5[i];
						ADC_IN6_sum += ADC_IN6[i];
        } 

        uint32_t ADC_IN4_avg = ADC_IN4_sum / (BUFFER_SIZE / 3);
				uint32_t ADC_IN5_avg = ADC_IN5_sum / (BUFFER_SIZE / 3);
				uint32_t ADC_IN6_avg = ADC_IN6_sum / (BUFFER_SIZE / 3);
        voltage_IN4 = (float)ADC_IN4_avg * 3.3f / 4096.0f;
				voltage_IN5 = (float)ADC_IN5_avg * 3.3f / 4096.0f;
				voltage_IN6 = (float)ADC_IN6_avg * 3.3f / 4096.0f;


        HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
        __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // �ٴν��ð봫���ж�
    }
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	adc_task();

	// 如果线宽还未标定完成，继续进行标定
	if(line_width_calibrated == 0)
	{
		Get_DATA();
	}

	GetTrace_Date();
}

#endif
