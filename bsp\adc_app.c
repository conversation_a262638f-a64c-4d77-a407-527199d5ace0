
#include "adc_app.h"
#include "stm32f1xx_hal_tim.h"

// 1 ��ѯ
// 2 DMA����ת��
// 3 DMA TIM ��ͨ���ɼ�

// --- ���� <PERSON><PERSON> ��ش��� ---


#define BUFFER_SIZE 300  // 确保是3的倍数，300/3=100个样本每通道
extern DMA_HandleTypeDef hdma_adc1;
uint32_t ADC_IN4[BUFFER_SIZE / 3];
uint32_t ADC_IN5[BUFFER_SIZE / 3];
uint32_t ADC_IN6[BUFFER_SIZE / 3];

__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;
uint8_t wave_analysis_flag = 0; // ���η�����־λ
uint8_t wave_query_type = 0;    // ���β�ѯ���ͣ�0=ȫ��, 1=����, 2=Ƶ��, 3=���ֵ
float error;

float Data_Out; //�������
float LEFT_THREASH = 180; //����ֵ
float RIGHT_THREASH = 185; //����ֵ
float LEFT_MAX = 2300; //�����ֵ
float RIGHT_MAX = 2200; //�����ֵ
float voltage_IN6; //左传感器的AD值
float voltage_IN5; //中传感器的AD值
float voltage_IN4; //右传感器的AD值

// 添加平均值变量声明
uint32_t ADC_IN4_avg = 0;
uint32_t ADC_IN5_avg = 0;
uint32_t ADC_IN6_avg = 0;

uint8_t state_1 = 1;
uint8_t state_2 = 1;


void Get_DATA()
{
	// 优化：使用容差比较而不是精确相等
	float tolerance = 0.05f;  // 50mV容差

	// 动态阈值计算
	float sensor_avg = (voltage_IN4 + voltage_IN5 + voltage_IN6) / 3.0f;
	float base_threshold = sensor_avg * 0.75f;  // 基础阈值为平均值的75%

	// 左侧阈值设置
	if(fabs(voltage_IN5 - voltage_IN6) < tolerance)
	{
		LEFT_THREASH = (voltage_IN5 + voltage_IN6) / 2.0f;
	}
	else
	{
		LEFT_THREASH = base_threshold;
	}

	// 右侧阈值设置
	if(fabs(voltage_IN5 - voltage_IN4) < tolerance)
	{
		RIGHT_THREASH = (voltage_IN5 + voltage_IN4) / 2.0f;
	}
	else
	{
		RIGHT_THREASH = base_threshold;
	}

	// 阈值合理性检查
	if(LEFT_THREASH < 0.5f) LEFT_THREASH = 0.5f;
	if(LEFT_THREASH > 2.5f) LEFT_THREASH = 2.5f;
	if(RIGHT_THREASH < 0.5f) RIGHT_THREASH = 0.5f;
	if(RIGHT_THREASH > 2.5f) RIGHT_THREASH = 2.5f;
}

float GetTrace_Date(void)
{
	// 先更新阈值
	Get_DATA();

	// 计算传感器状态
	uint8_t left_on_line = (voltage_IN6 < LEFT_THREASH) ? 1 : 0;
	uint8_t center_on_line = (voltage_IN5 < (LEFT_THREASH + RIGHT_THREASH) / 2.0f) ? 1 : 0;
	uint8_t right_on_line = (voltage_IN4 < RIGHT_THREASH) ? 1 : 0;

	// 计算传感器组合状态
	uint8_t sensor_pattern = (left_on_line << 2) | (center_on_line << 1) | right_on_line;

	// 根据传感器模式计算误差
	switch(sensor_pattern)
	{
		case 0b000:  // 000 - 全部在白色区域，可能丢线
			Data_Out = (voltage_IN6 - voltage_IN4) * 30.0f;  // 使用简单差值
			break;

		case 0b001:  // 001 - 只有右传感器在线上
			Data_Out = 100.0f;  // 向右偏移
			break;

		case 0b010:  // 010 - 只有中传感器在线上
			Data_Out = (voltage_IN6 - voltage_IN4) * 80.0f;  // 精确跟踪
			break;

		case 0b011:  // 011 - 中右传感器在线上
			Data_Out = 50.0f + (voltage_IN4 - voltage_IN5) * 40.0f;
			break;

		case 0b100:  // 100 - 只有左传感器在线上
			Data_Out = -100.0f;  // 向左偏移
			break;

		case 0b101:  // 101 - 左右传感器在线上（宽线或交叉）
			Data_Out = (voltage_IN6 - voltage_IN4) * 60.0f;
			break;

		case 0b110:// 110 - 左中传感器在线上
			Data_Out = -50.0f + (voltage_IN6 - voltage_IN5) * 40.0f;
			break;

		case 0b111:  // 111 - 全部传感器在线上（很宽的线）
			Data_Out = (voltage_IN6 - voltage_IN4) * 40.0f;
			break;

		default:
			Data_Out = 0;
			break;
	}

	// 限制输出范围
	if(Data_Out > 150.0f) Data_Out = 150.0f;
	if(Data_Out < -150.0f) Data_Out = -150.0f;

	return Data_Out;
}


void adc_tim_dma_init(void)
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // ���ð봫���ж�
}

// ��usart_app.c������Ϊextern

void adc_task(void)
{
	// 优化的ADC数据处理
	uint32_t samples_per_channel = BUFFER_SIZE / 3;

	// 数据有效性检查
	if(samples_per_channel == 0) return;

	// 一次循环完成数据分离和累加，提高效率
	uint32_t ADC_IN4_sum = 0;
	uint32_t ADC_IN5_sum = 0;
	uint32_t ADC_IN6_sum = 0;

	uint32_t valid_samples = 0;

	for (uint16_t i = 0; i < samples_per_channel; i++)
	{
		// 获取原始ADC值
		uint32_t adc4_val = adc_val_buffer[i * 3 + 0];  // PA4 - 右传感器
		uint32_t adc5_val = adc_val_buffer[i * 3 + 1];  // PA5 - 中传感器
		uint32_t adc6_val = adc_val_buffer[i * 3 + 2];  // PA6 - 左传感器

		// 数据有效性检查（12位ADC范围：0-4095）
		if(adc4_val <= 4095 && adc5_val <= 4095 && adc6_val <= 4095)
		{
			// 存储到数组（保持兼容性）
			ADC_IN4[i] = adc4_val;
			ADC_IN5[i] = adc5_val;
			ADC_IN6[i] = adc6_val;

			// 累加有效数据
			ADC_IN4_sum += adc4_val;
			ADC_IN5_sum += adc5_val;
			ADC_IN6_sum += adc6_val;
			valid_samples++;
		}
	}

	// 确保有有效数据
	if(valid_samples > 0)
	{
		// 计算平均值
		ADC_IN4_avg = ADC_IN4_sum / valid_samples;
		ADC_IN5_avg = ADC_IN5_sum / valid_samples;
		ADC_IN6_avg = ADC_IN6_sum / valid_samples;

		// 转换为电压值（12位ADC，参考电压3.3V）
		float voltage_IN4_new = ((float)ADC_IN4_avg * 3.3f) / 4096.0f;
		float voltage_IN5_new = ((float)ADC_IN5_avg * 3.3f) / 4096.0f;
		float voltage_IN6_new = ((float)ADC_IN6_avg * 3.3f) / 4096.0f;

		// 简单的低通滤波，减少噪声
		static uint8_t filter_init = 0;
		if(!filter_init)
		{
			voltage_IN4 = voltage_IN4_new;
			voltage_IN5 = voltage_IN5_new;
			voltage_IN6 = voltage_IN6_new;
			filter_init = 1;
		}
		else
		{
			float alpha = 0.3f;  // 滤波系数
			voltage_IN4 = voltage_IN4 * (1.0f - alpha) + voltage_IN4_new * alpha;
			voltage_IN5 = voltage_IN5 * (1.0f - alpha) + voltage_IN5_new * alpha;
			voltage_IN6 = voltage_IN6 * (1.0f - alpha) + voltage_IN6_new * alpha;
		}
	}
}

// 获取传感器状态信息（用于调试）
void Get_Sensor_Status_Info(uint8_t *left_status, uint8_t *center_status, uint8_t *right_status,
                            float *left_thresh, float *right_thresh)
{
	// 更新阈值
	Get_DATA();

	// 计算传感器状态
	*left_status = (voltage_IN6 < LEFT_THREASH) ? 1 : 0;
	*center_status = (voltage_IN5 < (LEFT_THREASH + RIGHT_THREASH) / 2.0f) ? 1 : 0;
	*right_status = (voltage_IN4 < RIGHT_THREASH) ? 1 : 0;

	// 返回阈值
	*left_thresh = LEFT_THREASH;
	*right_thresh = RIGHT_THREASH;
}

// 获取传感器模式（用于调试）
uint8_t Get_Sensor_Pattern(void)
{
	uint8_t left_on_line = (voltage_IN6 < LEFT_THREASH) ? 1 : 0;
	uint8_t center_on_line = (voltage_IN5 < (LEFT_THREASH + RIGHT_THREASH) / 2.0f) ? 1 : 0;
	uint8_t right_on_line = (voltage_IN4 < RIGHT_THREASH) ? 1 : 0;

	return (left_on_line << 2) | (center_on_line << 1) | right_on_line;
}
