
#include "adc_app.h"
#include "stm32f1xx_hal_tim.h"

// 1 ��ѯ
// 2 DMA����ת��
// 3 DMA TIM ��ͨ���ɼ�

// --- ���� <PERSON><PERSON> ��ش��� ---


#define BUFFER_SIZE 300  // 确保是3的倍数，300/3=100个样本每通道
extern DMA_HandleTypeDef hdma_adc1;
uint32_t ADC_IN4[BUFFER_SIZE / 3];
uint32_t ADC_IN5[BUFFER_SIZE / 3];
uint32_t ADC_IN6[BUFFER_SIZE / 3];

__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;
uint8_t wave_analysis_flag = 0; // ���η�����־λ
uint8_t wave_query_type = 0;    // ���β�ѯ���ͣ�0=ȫ��, 1=����, 2=Ƶ��, 3=���ֵ
float error;

float Data_Out; //�������
float LEFT_THREASH; //����ֵ
float RIGHT_THREASH; //����ֵ
float LEFT_MAX = 3.3; //�����ֵ
float RIGHT_MAX = 3.3; //�����ֵ
float voltage_IN6; //左传感器的AD值
float voltage_IN5; //中传感器的AD值
float voltage_IN4; //右传感器的AD值

// 添加平均值变量声明
uint32_t ADC_IN4_avg;
uint32_t ADC_IN5_avg;
uint32_t ADC_IN6_avg;

void Get_DATA()
{
	if(voltage_IN5 == voltage_IN6)
	{
		LEFT_THREASH = voltage_IN6;
	}
	if(voltage_IN5 == voltage_IN4)
	{
		RIGHT_THREASH = voltage_IN4;
	}
}


float GetTrace_Date(void)
{
	Data_Out=(voltage_IN6-voltage_IN4);
	if((Data_Out>0)&&((voltage_IN5<LEFT_THREASH)))
	{
		Data_Out=(2*LEFT_MAX-voltage_IN6)*2;
	}
	else if((Data_Out<0)&&((voltage_IN5<RIGHT_THREASH)))
	{
		Data_Out=(2*RIGHT_MAX-voltage_IN4)*2;
	}
	return Data_Out;
}


void adc_tim_dma_init(void)
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // ���ð봫���ж�
}

// ��usart_app.c������Ϊextern

void adc_task(void)
{
	// DMA循环模式：数据会持续更新到adc_val_buffer
	// 每次调用adc_task()时直接处理当前缓冲区的数据

	// --- 数据采集和处理 ---
	// 分离三个通道的数据到各自数组
	for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
	{
		ADC_IN4[i] = adc_val_buffer[i * 3 + 0];     // 第1个通道 (PA4)
		ADC_IN5[i] = adc_val_buffer[i * 3 + 1];     // 第2个通道 (PA5)
		ADC_IN6[i] = adc_val_buffer[i * 3 + 2];     // 第3个通道 (PA6)
	}
	// 计算每个通道的累加和
	uint32_t ADC_IN4_sum = 0;
	uint32_t ADC_IN5_sum = 0;
	uint32_t ADC_IN6_sum = 0;

	for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
	{
		ADC_IN4_sum += ADC_IN4[i];
		ADC_IN5_sum += ADC_IN5[i];
		ADC_IN6_sum += ADC_IN6[i];
	}

	// 计算平均值
	voltage_IN4 = ADC_IN4_sum / (BUFFER_SIZE / 3);
	voltage_IN5 = ADC_IN5_sum / (BUFFER_SIZE / 3);
	voltage_IN6 = ADC_IN6_sum / (BUFFER_SIZE / 3);

	// 转换为电压值 (12位ADC: 0-4095对应0-3.3V)
//	voltage_IN4 = ((float)ADC_IN4_avg * 3.3f) / 4096.0f;
//	voltage_IN5 = ((float)ADC_IN5_avg * 3.3f) / 4096.0f;
//	voltage_IN6 = ((float)ADC_IN6_avg * 3.3f) / 4096.0f;

	// 注意：由于DMA配置为循环模式(DMA_CIRCULAR)，
	// DMA会自动重复采集，不需要手动重启
			// ע�⣺�����ʱ������������ ADC �ģ����ܲ���Ҫ�ֶ�ֹͣ/���� DMA
			// ��Ҫ���� TIM3 �� ADC �ľ������þ����Ƿ���Ҫ��������
}


