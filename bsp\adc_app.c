
#include "adc_app.h"
#include "stm32f1xx_hal_tim.h"

// 1 ��ѯ
// 2 DMA����ת��
// 3 DMA TIM ��ͨ���ɼ�

// --- ���� <PERSON><PERSON> ��ش��� ---


#define BUFFER_SIZE 300  // 确保是3的倍数，300/3=100个样本每通道
extern DMA_HandleTypeDef hdma_adc1;
uint32_t ADC_IN4[BUFFER_SIZE / 3];
uint32_t ADC_IN5[BUFFER_SIZE / 3];
uint32_t ADC_IN6[BUFFER_SIZE / 3];

__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;
uint8_t wave_analysis_flag = 0; // ���η�����־λ
uint8_t wave_query_type = 0;    // ���β�ѯ���ͣ�0=ȫ��, 1=����, 2=Ƶ��, 3=���ֵ
float error;

float Data_Out; //�������
float LEFT_THREASH = 180; //����ֵ
float RIGHT_THREASH = 185; //����ֵ
float LEFT_MAX = 2300; //�����ֵ
float RIGHT_MAX = 2200; //�����ֵ
float voltage_IN6; //左传感器的AD值
float voltage_IN5; //中传感器的AD值
float voltage_IN4; //右传感器的AD值

// 添加平均值变量声明
uint32_t ADC_IN4_avg = 0;
uint32_t ADC_IN5_avg = 0;
uint32_t ADC_IN6_avg = 0;

uint8_t state_1 = 1;
uint8_t state_2 = 1;


void Get_DATA()
{
	if(voltage_IN5 == voltage_IN6)
	{
		LEFT_THREASH = voltage_IN6;
	}
	if(voltage_IN5 == voltage_IN4)
	{
		RIGHT_THREASH = voltage_IN4;
	}
}

float GetTrace_Date(void)
{
	Data_Out=(voltage_IN6-voltage_IN4);
	if((Data_Out>0)&&((voltage_IN5<LEFT_THREASH)))
	{
		Data_Out=(2*LEFT_MAX-voltage_IN6)*2;
	}
	else if((Data_Out<0)&&((voltage_IN5<RIGHT_THREASH)))
	{
		Data_Out=-(2*RIGHT_MAX-voltage_IN4)*2;
	}
	return Data_Out;
}


void adc_tim_dma_init(void)
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // ���ð봫���ж�
}

// ��usart_app.c������Ϊextern

void adc_task(void)
{
		// --- �����ɼ��������� ---
		// ʾ�������������������ݸ��Ƶ���һ�������� (ԭ��δ֪����ԭ�߼�����)
		for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
		{
				ADC_IN4[i] = adc_val_buffer[i * 3];
				ADC_IN5[i] = adc_val_buffer[i * 3 + 1]; // �� ADC ���ݴ�����Ϊ dac_val_buffer ������
				ADC_IN6[i] = adc_val_buffer[i * 3 + 2];

		}
		uint32_t ADC_IN4_sum = 0;
		uint32_t ADC_IN5_sum = 0;
		uint32_t ADC_IN6_sum = 0;
		// �� res_val_buffer �е�����ת��Ϊ��ѹֵ
		for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
		{
				ADC_IN4_sum += ADC_IN4[i];
				ADC_IN5_sum += ADC_IN5[i];
				ADC_IN6_sum += ADC_IN6[i];
		}

		voltage_IN4 = ADC_IN4_sum / (BUFFER_SIZE / 3);
		voltage_IN5 = ADC_IN5_sum / (BUFFER_SIZE / 3);
		voltage_IN6 = ADC_IN6_sum / (BUFFER_SIZE / 3);

		// --- ������� ---

		// ��մ��������� (��ѡ��ȡ���ں����߼�)
		// memset(dac_val_buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));

		// ��� ADC DMA �������ͱ�־λ��׼����һ�βɼ�
		// memset(adc_val_buffer, 0, sizeof(uint32_t) * BUFFER_SIZE); // ���ԭʼ���� (�����Ҫ)

		// �������� ADC DMA ������һ�βɼ�
		// ע�⣺�����ʱ������������ ADC �ģ����ܲ���Ҫ�ֶ�ֹͣ/���� DMA
		// ��Ҫ���� TIM3 �� ADC �ľ������þ����Ƿ���Ҫ��������
}
