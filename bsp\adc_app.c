
#include "adc_app.h"
#include "stm32f1xx_hal_tim.h"
#include <math.h>

// 1 ��ѯ
// 2 DMA����ת��
// 3 DMA TIM ��ͨ���ɼ�
#define ADC_MODE (3)

// --- ���� AD<PERSON> ��ش��� ---

#if ADC_MODE == 1

#elif ADC_MODE == 3

#define BUFFER_SIZE 2046  
extern DMA_HandleTypeDef hdma_adc1;
uint32_t ADC_IN4[BUFFER_SIZE / 3];
uint32_t ADC_IN5[BUFFER_SIZE / 3];
uint32_t ADC_IN6[BUFFER_SIZE / 3];

__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;
uint8_t wave_analysis_flag = 0; // ���η�����־λ
uint8_t wave_query_type = 0;    // ���β�ѯ���ͣ�0=ȫ��, 1=����, 2=Ƶ��, 3=���ֵ
float error;

float Data_Out; //�������
float LEFT_THREASH; //����ֵ
float RIGHT_THREASH; //����ֵ
float LEFT_MAX = 3.3; //�����ֵ
float RIGHT_MAX = 3.3; //�����ֵ
float voltage_IN6; //�󴫸������ADֵ
float voltage_IN5;
float voltage_IN4; //�Ҵ��������ADֵ
uint8_t state_1 = 0;
uint8_t state_2 = 0;
uint8_t line_width_calibrated = 0;  // 线宽标定完成标志
uint8_t system_ready = 0;           // 系统准备就绪标志

void Get_DATA()
{
	// 改进的线宽识别逻辑：使用阈值范围而不是精确相等
	float tolerance = 0.05f;  // 允许的电压差值容忍度

	// 左侧线宽识别：当中间传感器与左传感器电压接近时
	if(fabs(voltage_IN5 - voltage_IN6) < tolerance && state_1 == 0)
	{
		LEFT_THREASH = voltage_IN6;
		state_1 = 1;
	}

	// 右侧线宽识别：当中间传感器与右传感器电压接近时
	if(fabs(voltage_IN5 - voltage_IN4) < tolerance && state_2 == 0)
	{
		RIGHT_THREASH = voltage_IN4;
		state_2 = 1;
	}

	// 当左右阈值都识别完成时，标记线宽标定完成
	if(state_1 == 1 && state_2 == 1 && line_width_calibrated == 0)
	{
		line_width_calibrated = 1;
		system_ready = 1;  // 系统准备就绪，可以开始循迹
	}
}

// 新增：获取线宽标定状态的函数
uint8_t Get_LineWidth_Status(void)
{
	return line_width_calibrated;
}

// 新增：重置线宽标定的函数
void Reset_LineWidth_Calibration(void)
{
	state_1 = 0;
	state_2 = 0;
	line_width_calibrated = 0;
	system_ready = 0;
	LEFT_THREASH = 0;
	RIGHT_THREASH = 0;
}


float GetTrace_Date(void)
{
	Data_Out=(voltage_IN6-voltage_IN4);
	if((Data_Out>0)&&((voltage_IN5<LEFT_THREASH)))
	{
		Data_Out=(2*LEFT_MAX-voltage_IN6)*2;
	}
	else if((Data_Out<0)&&((voltage_IN5<RIGHT_THREASH)))
	{
		Data_Out=(2*RIGHT_MAX-voltage_IN4)*2;
	}
	return Data_Out;
}


void adc_tim_dma_init(void)
{
		HAL_TIM_Base_Start_IT(&htim3);
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // ���ð봫���ж�
}

// ��usart_app.c������Ϊextern

void adc_task(void)
{
    // һ������ת�� 3(����) + 12.5(ת��) = 15.5 ADCʱ������
    // ���� ADC ʱ�� 14MHz (���� HSI/PLL), һ��ת��ʱ��: 15.5 / 14MHz ~= 1.1 us
    // BUFFER_SIZE ��ת����ʱ��: 1000 * 1.1 us = 1.1 ms (����ֵ)
    // ��ʱ������Ƶ����Ҫƥ��������ʻ����

    if (AdcConvEnd) // ���ת����ɱ�־
    {
        // --- �����ɼ��������� ---
        // ʾ�������������������ݸ��Ƶ���һ�������� (ԭ��δ֪����ԭ�߼�����)
        for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
        {            
						ADC_IN4[i] = adc_val_buffer[i * 3];
            ADC_IN5[i] = adc_val_buffer[i * 3 + 1]; // �� ADC ���ݴ�����Ϊ dac_val_buffer ������
						ADC_IN6[i] = adc_val_buffer[i * 3 + 2]; 
	
        }
        uint32_t ADC_IN4_sum = 0;
				uint32_t ADC_IN5_sum = 0;
				uint32_t ADC_IN6_sum = 0;
        // �� res_val_buffer �е�����ת��Ϊ��ѹֵ
        for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
        {
            ADC_IN4_sum += ADC_IN4[i];
						ADC_IN5_sum += ADC_IN5[i];
						ADC_IN6_sum += ADC_IN6[i];
        } 

        uint32_t ADC_IN4_avg = ADC_IN4_sum / (BUFFER_SIZE / 3);
				uint32_t ADC_IN5_avg = ADC_IN5_sum / (BUFFER_SIZE / 3);
				uint32_t ADC_IN6_avg = ADC_IN6_sum / (BUFFER_SIZE / 3);
        voltage_IN4 = (float)ADC_IN4_avg * 3.3f / 4096.0f;
				voltage_IN5 = (float)ADC_IN5_avg * 3.3f / 4096.0f;
				voltage_IN6 = (float)ADC_IN6_avg * 3.3f / 4096.0f;

        // --- ������� ---

        // ��մ��������� (��ѡ��ȡ���ں����߼�)
        // memset(dac_val_buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));

        // ��� ADC DMA �������ͱ�־λ��׼����һ�βɼ�
        // memset(adc_val_buffer, 0, sizeof(uint32_t) * BUFFER_SIZE); // ���ԭʼ���� (�����Ҫ)

        // �������� ADC DMA ������һ�βɼ�
        // ע�⣺�����ʱ������������ ADC �ģ����ܲ���Ҫ�ֶ�ֹͣ/���� DMA
        // ��Ҫ���� TIM3 �� ADC �ľ������þ����Ƿ���Ҫ��������
        HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
        __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // �ٴν��ð봫���ж�
    }
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	adc_task();

	// 如果线宽还未标定完成，继续进行标定
	if(line_width_calibrated == 0)
	{
		Get_DATA();
	}
	else
	{
		// 线宽标定完成后，开始计算循迹误差
		error = GetTrace_Date();
	}
}

#endif
