
#include "adc_app.h"
#include "stm32f1xx_hal_tim.h"

// 1 ��ѯ
// 2 DMA����ת��
// 3 DMA TIM ��ͨ���ɼ�

// --- ���� <PERSON><PERSON> ��ش��� ---


#define BUFFER_SIZE 300  // 确保是3的倍数，300/3=100个样本每通道
extern DMA_HandleTypeDef hdma_adc1;
uint32_t ADC_IN4[BUFFER_SIZE / 3];
uint32_t ADC_IN5[BUFFER_SIZE / 3];
uint32_t ADC_IN6[BUFFER_SIZE / 3];

__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;
uint8_t wave_analysis_flag = 0; // ���η�����־λ
uint8_t wave_query_type = 0;    // ���β�ѯ���ͣ�0=ȫ��, 1=����, 2=Ƶ��, 3=���ֵ
float error;

float Data_Out; //�������
float LEFT_THREASH = 180; //����ֵ
float RIGHT_THREASH = 185; //����ֵ
float LEFT_MAX = 2300; //�����ֵ
float RIGHT_MAX = 2200; //�����ֵ
float voltage_IN6; //左传感器的AD值
float voltage_IN5; //中传感器的AD值
float voltage_IN4; //右传感器的AD值

// 添加平均值变量声明
uint32_t ADC_IN4_avg = 0;
uint32_t ADC_IN5_avg = 0;
uint32_t ADC_IN6_avg = 0;

uint8_t state_1 = 1;
uint8_t state_2 = 1;

// 线宽检测相关变量
uint8_t detected_line_width = 0;  // 检测到的线宽类型：0=无线，1=窄线，2=中等，3=宽线
float line_width_mm = 0.0f;       // 估算的线宽（毫米）
uint8_t sensors_on_line_count = 0; // 在线上的传感器数量


// 优化的线宽识别函数
void Get_DATA(void)
{
	// 自适应阈值计算
	float sensor_avg = (voltage_IN4 + voltage_IN5 + voltage_IN6) / 3.0f;
	float adaptive_threshold = sensor_avg * 0.8f;  // 动态阈值

	// 更智能的阈值设置
	if(voltage_IN5 < adaptive_threshold)
	{
		// 中传感器在线上，设置左右阈值
		LEFT_THREASH = adaptive_threshold;
		RIGHT_THREASH = adaptive_threshold;
	}
	else
	{
		// 传感器在白色区域，使用固定阈值
		LEFT_THREASH = 1.5f;   // 可根据实际情况调整
		RIGHT_THREASH = 1.5f;
	}
}

// 优化的循迹误差计算函数
float GetTrace_Date(void)
{
	// 先更新阈值
	Get_DATA();

	// 计算传感器状态（0=白色，1=黑色）
	uint8_t left_on_line = (voltage_IN6 < LEFT_THREASH) ? 1 : 0;
	uint8_t center_on_line = (voltage_IN5 < (LEFT_THREASH + RIGHT_THREASH) / 2.0f) ? 1 : 0;
	uint8_t right_on_line = (voltage_IN4 < RIGHT_THREASH) ? 1 : 0;

	// 根据传感器组合计算误差
	if(center_on_line)
	{
		// 中传感器在线上，使用精确的位置计算
		float left_intensity = (LEFT_THREASH - voltage_IN6) / LEFT_THREASH;
		float right_intensity = (RIGHT_THREASH - voltage_IN4) / RIGHT_THREASH;

		// 限制强度值范围
		if(left_intensity < 0) left_intensity = 0;
		if(left_intensity > 1) left_intensity = 1;
		if(right_intensity < 0) right_intensity = 0;
		if(right_intensity > 1) right_intensity = 1;

		// 加权位置计算
		Data_Out = (left_intensity - right_intensity) * 100.0f;
	}
	else if(left_on_line && !right_on_line)
	{
		// 只有左传感器在线上，向左偏移
		Data_Out = -150.0f;
	}
	else if(!left_on_line && right_on_line)
	{
		// 只有右传感器在线上，向右偏移
		Data_Out = 150.0f;
	}
	else if(left_on_line && right_on_line)
	{
		// 左右都在线上，可能是宽线或交叉
		Data_Out = (voltage_IN6 - voltage_IN4) * 50.0f;
	}
	else
	{
		// 所有传感器都不在线上，可能丢线
		// 保持上次的误差值或使用简单差值
		Data_Out = (voltage_IN6 - voltage_IN4) * 30.0f;
	}

	// 限制输出范围
	if(Data_Out > 200.0f) Data_Out = 200.0f;
	if(Data_Out < -200.0f) Data_Out = -200.0f;

	return Data_Out;
}


void adc_tim_dma_init(void)
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // ���ð봫���ж�
}

// ��usart_app.c������Ϊextern

void adc_task(void)
{
	// 优化的ADC数据处理算法

	// 检查缓冲区大小有效性
	if(BUFFER_SIZE < 3) return;

	uint32_t samples_per_channel = BUFFER_SIZE / 3;

	// 一次循环完成数据分离和累加，提高效率
	uint32_t ADC_IN4_sum = 0;
	uint32_t ADC_IN5_sum = 0;
	uint32_t ADC_IN6_sum = 0;

	uint32_t ADC_IN4_min = 4095, ADC_IN4_max = 0;
	uint32_t ADC_IN5_min = 4095, ADC_IN5_max = 0;
	uint32_t ADC_IN6_min = 4095, ADC_IN6_max = 0;

	// 单次循环处理所有数据
	for (uint16_t i = 0; i < samples_per_channel; i++)
	{
		// 获取当前样本
		uint32_t adc4_val = adc_val_buffer[i * 3 + 0];  // PA4 - 右传感器
		uint32_t adc5_val = adc_val_buffer[i * 3 + 1];  // PA5 - 中传感器
		uint32_t adc6_val = adc_val_buffer[i * 3 + 2];  // PA6 - 左传感器

		// 数据有效性检查（12位ADC范围：0-4095）
		if(adc4_val <= 4095 && adc5_val <= 4095 && adc6_val <= 4095)
		{
			// 累加有效数据
			ADC_IN4_sum += adc4_val;
			ADC_IN5_sum += adc5_val;
			ADC_IN6_sum += adc6_val;

			// 记录最值用于噪声检测
			if(adc4_val < ADC_IN4_min) ADC_IN4_min = adc4_val;
			if(adc4_val > ADC_IN4_max) ADC_IN4_max = adc4_val;
			if(adc5_val < ADC_IN5_min) ADC_IN5_min = adc5_val;
			if(adc5_val > ADC_IN5_max) ADC_IN5_max = adc5_val;
			if(adc6_val < ADC_IN6_min) ADC_IN6_min = adc6_val;
			if(adc6_val > ADC_IN6_max) ADC_IN6_max = adc6_val;

			// 存储到数组（保持兼容性）
			ADC_IN4[i] = adc4_val;
			ADC_IN5[i] = adc5_val;
			ADC_IN6[i] = adc6_val;
		}
	}

	// 计算平均值
	ADC_IN4_avg = ADC_IN4_sum / samples_per_channel;
	ADC_IN5_avg = ADC_IN5_sum / samples_per_channel;
	ADC_IN6_avg = ADC_IN6_sum / samples_per_channel;

	// 转换为电压值（12位ADC，参考电压3.3V）
	voltage_IN4 = ((float)ADC_IN4_avg * 3.3f) / 4096.0f;
	voltage_IN5 = ((float)ADC_IN5_avg * 3.3f) / 4096.0f;
	voltage_IN6 = ((float)ADC_IN6_avg * 3.3f) / 4096.0f;

	// 简单的数字滤波（移动平均）
	static float voltage_IN4_filtered = 0;
	static float voltage_IN5_filtered = 0;
	static float voltage_IN6_filtered = 0;
	static uint8_t filter_init = 0;

	if(!filter_init)
	{
		// 首次运行，直接使用当前值
		voltage_IN4_filtered = voltage_IN4;
		voltage_IN5_filtered = voltage_IN5;
		voltage_IN6_filtered = voltage_IN6;
		filter_init = 1;
	}
	else
	{
		// 低通滤波：新值 = 0.8 * 旧值 + 0.2 * 新值
		float alpha = 0.2f;  // 滤波系数，可调节
		voltage_IN4_filtered = voltage_IN4_filtered * (1.0f - alpha) + voltage_IN4 * alpha;
		voltage_IN5_filtered = voltage_IN5_filtered * (1.0f - alpha) + voltage_IN5 * alpha;
		voltage_IN6_filtered = voltage_IN6_filtered * (1.0f - alpha) + voltage_IN6 * alpha;
	}

	// 使用滤波后的值
	voltage_IN4 = voltage_IN4_filtered;
	voltage_IN5 = voltage_IN5_filtered;
	voltage_IN6 = voltage_IN6_filtered;

	// 检测线宽
	Detect_Line_Width();
}

// 线宽检测函数
void Detect_Line_Width(void)
{
	// 设置检测阈值（可根据实际情况调整）
	float detection_threshold = 1.5f;  // 1.5V作为黑白分界

	// 统计在线上的传感器数量
	sensors_on_line_count = 0;
	uint8_t left_on_line = 0, center_on_line = 0, right_on_line = 0;

	if(voltage_IN6 < detection_threshold) { left_on_line = 1; sensors_on_line_count++; }
	if(voltage_IN5 < detection_threshold) { center_on_line = 1; sensors_on_line_count++; }
	if(voltage_IN4 < detection_threshold) { right_on_line = 1; sensors_on_line_count++; }

	// 根据传感器组合判断线宽类型
	if(sensors_on_line_count == 0)
	{
		detected_line_width = 0;  // 无线/丢线
		line_width_mm = 0.0f;
	}
	else if(sensors_on_line_count == 1)
	{
		detected_line_width = 1;  // 窄线（5-10mm）
		line_width_mm = 8.0f;
	}
	else if(sensors_on_line_count == 2)
	{
		detected_line_width = 2;  // 中等线宽（15-20mm）
		line_width_mm = 18.0f;
	}
	else if(sensors_on_line_count == 3)
	{
		detected_line_width = 3;  // 宽线（25mm以上）
		line_width_mm = 30.0f;
	}
}

// 获取检测到的线宽类型
uint8_t Get_Line_Width_Type(void)
{
	return detected_line_width;
}

// 获取估算的线宽（毫米）
float Get_Line_Width_MM(void)
{
	return line_width_mm;
}

// 获取在线上的传感器数量
uint8_t Get_Sensors_On_Line_Count(void)
{
	return sensors_on_line_count;
}

// 获取详细的传感器状态
void Get_Sensor_Status(uint8_t *left_status, uint8_t *center_status, uint8_t *right_status)
{
	float detection_threshold = 1.5f;

	*left_status = (voltage_IN6 < detection_threshold) ? 1 : 0;
	*center_status = (voltage_IN5 < detection_threshold) ? 1 : 0;
	*right_status = (voltage_IN4 < detection_threshold) ? 1 : 0;
}

// 获取线宽检测的详细信息（用于调试）
void Get_Line_Width_Info(uint8_t *width_type, float *width_mm, uint8_t *sensor_count,
                         float *left_voltage, float *center_voltage, float *right_voltage)
{
	*width_type = detected_line_width;
	*width_mm = line_width_mm;
	*sensor_count = sensors_on_line_count;
	*left_voltage = voltage_IN6;
	*center_voltage = voltage_IN5;
	*right_voltage = voltage_IN4;
}


