
#include "adc_app.h"
#include "stm32f1xx_hal_tim.h"

// 1 ��ѯ
// 2 DMA����ת��
// 3 DMA TIM ��ͨ���ɼ�
#define ADC_MODE (3)

// --- ���� ADC ��ش��� ---

#if ADC_MODE == 1

#elif ADC_MODE == 3

#define BUFFER_SIZE 300   // 减小缓冲区大小，提高响应速度
extern DMA_HandleTypeDef hdma_adc1;
uint32_t ADC_IN4[BUFFER_SIZE / 3];
uint32_t ADC_IN5[BUFFER_SIZE / 3];
uint32_t ADC_IN6[BUFFER_SIZE / 3];

__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;

// 添加滤波相关变量
float voltage_IN4_filtered = 0.0f;
float voltage_IN5_filtered = 0.0f;
float voltage_IN6_filtered = 0.0f;
#define FILTER_ALPHA 0.1f  // 低通滤波系数，越小越平滑
uint8_t wave_analysis_flag = 0; // ���η�����־λ
uint8_t wave_query_type = 0;    // ���β�ѯ���ͣ�0=ȫ��, 1=����, 2=Ƶ��, 3=���ֵ
float error;

float Data_Out; //�������
float LEFT_THREASH; //����ֵ
float RIGHT_THREASH; //����ֵ
float LEFT_MAX = 3.3; //�����ֵ
float RIGHT_MAX = 3.3; //�����ֵ
float voltage_IN6; //�󴫸������ADֵ
float voltage_IN5;
float voltage_IN4; //右传感器的AD值

void Get_DATA()
{
	if(voltage_IN5 == voltage_IN6)
	{
		LEFT_THREASH = voltage_IN6;
	}
	if(voltage_IN5 == voltage_IN4)
	{
		RIGHT_THREASH = voltage_IN4;
	}
}


float GetTrace_Date(void)
{
	Data_Out=(voltage_IN6-voltage_IN4);
	if((Data_Out>0)&&((voltage_IN5<LEFT_THREASH)))
	{
		Data_Out=(2*LEFT_MAX-voltage_IN6)*2;
	}
	else if((Data_Out<0)&&((voltage_IN5<RIGHT_THREASH)))
	{
		Data_Out=(2*RIGHT_MAX-voltage_IN4)*2;
	}
	return Data_Out;
}


void adc_tim_dma_init(void)
{
    // 初始化滤波器
    voltage_IN4_filtered = 1.65f;  // 初始值设为中间电压
    voltage_IN5_filtered = 1.65f;
    voltage_IN6_filtered = 1.65f;

    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // 禁用半传输中断
}

// ��usart_app.c������Ϊextern

void adc_task(void)
{
			// --- �����ɼ��������� ---
			// ʾ�������������������ݸ��Ƶ���һ�������� (ԭ��δ֪����ԭ�߼�����)
			for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
			{            
					ADC_IN4[i] = adc_val_buffer[i * 3];
					ADC_IN5[i] = adc_val_buffer[i * 3 + 1]; // �� ADC ���ݴ�����Ϊ dac_val_buffer ������
					ADC_IN6[i] = adc_val_buffer[i * 3 + 2]; 

			}
			uint32_t ADC_IN4_sum = 0;
			uint32_t ADC_IN5_sum = 0;
			uint32_t ADC_IN6_sum = 0;
			// �� res_val_buffer �е�����ת��Ϊ��ѹֵ
			for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
			{
					ADC_IN4_sum += ADC_IN4[i];
					ADC_IN5_sum += ADC_IN5[i];
					ADC_IN6_sum += ADC_IN6[i];
			} 

			uint32_t ADC_IN4_avg = ADC_IN4_sum / (BUFFER_SIZE / 3);
			uint32_t ADC_IN5_avg = ADC_IN5_sum / (BUFFER_SIZE / 3);
			uint32_t ADC_IN6_avg = ADC_IN6_sum / (BUFFER_SIZE / 3);
			// 转换为电压值
			float voltage_IN4_raw = (float)ADC_IN4_avg * 3.3f / 4096.0f;
			float voltage_IN5_raw = (float)ADC_IN5_avg * 3.3f / 4096.0f;
			float voltage_IN6_raw = (float)ADC_IN6_avg * 3.3f / 4096.0f;

			// 应用低通滤波器，减少跳动
			voltage_IN4_filtered = voltage_IN4_filtered * (1.0f - FILTER_ALPHA) + voltage_IN4_raw * FILTER_ALPHA;
			voltage_IN5_filtered = voltage_IN5_filtered * (1.0f - FILTER_ALPHA) + voltage_IN5_raw * FILTER_ALPHA;
			voltage_IN6_filtered = voltage_IN6_filtered * (1.0f - FILTER_ALPHA) + voltage_IN6_raw * FILTER_ALPHA;

			// 使用滤波后的数据
			voltage_IN4 = voltage_IN4_filtered;
			voltage_IN5 = voltage_IN5_filtered;
			voltage_IN6 = voltage_IN6_filtered;

			// --- ������� ---

			// ��մ��������� (��ѡ��ȡ���ں����߼�)
			// memset(dac_val_buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));

			// ��� ADC DMA �������ͱ�־λ��׼����һ�βɼ�
			// memset(adc_val_buffer, 0, sizeof(uint32_t) * BUFFER_SIZE); // ���ԭʼ���� (�����Ҫ)

			// �������� ADC DMA ������һ�βɼ�
			// ע�⣺�����ʱ������������ ADC �ģ����ܲ���Ҫ�ֶ�ֹͣ/���� DMA
			// ��Ҫ���� TIM3 �� ADC �ľ������þ����Ƿ���Ҫ��������
			// 移除重复的DMA启动，避免数据跳动
			// HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
			// __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
}

// 新增：设置滤波强度的函数
void Set_ADC_Filter_Alpha(float alpha)
{
	if(alpha >= 0.01f && alpha <= 1.0f)
	{
		// 修改滤波系数，alpha越小越平滑，但响应越慢
		// 这里需要修改宏定义，实际使用中可以用全局变量
	}
}

// 新增：获取原始ADC数据的函数（未滤波）
void Get_Raw_ADC_Data(float *raw_v4, float *raw_v5, float *raw_v6)
{
	// 这里可以返回未滤波的原始数据用于调试
	*raw_v4 = voltage_IN4;
	*raw_v5 = voltage_IN5;
	*raw_v6 = voltage_IN6;
}

#endif
