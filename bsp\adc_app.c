
#include "adc_app.h"
#include "stm32f1xx_hal_tim.h"

// 1 ��ѯ
// 2 DMA����ת��
// 3 DMA TIM ��ͨ���ɼ�

// --- ���� <PERSON><PERSON> ��ش��� ---


#define BUFFER_SIZE 300  // 确保是3的倍数，300/3=100个样本每通道
extern DMA_HandleTypeDef hdma_adc1;
uint32_t ADC_IN4[BUFFER_SIZE / 3];
uint32_t ADC_IN5[BUFFER_SIZE / 3];
uint32_t ADC_IN6[BUFFER_SIZE / 3];

__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;
uint8_t wave_analysis_flag = 0; // ���η�����־λ
uint8_t wave_query_type = 0;    // ���β�ѯ���ͣ�0=ȫ��, 1=����, 2=Ƶ��, 3=���ֵ
float error;

float Data_Out; //�������
float LEFT_THREASH = 180; //����ֵ
float RIGHT_THREASH = 185; //����ֵ
float LEFT_MAX = 2300; //�����ֵ
float RIGHT_MAX = 2200; //�����ֵ
float voltage_IN6; //左传感器的AD值
float voltage_IN5; //中传感器的AD值
float voltage_IN4; //右传感器的AD值

// 添加平均值变量声明
uint32_t ADC_IN4_avg = 0;
uint32_t ADC_IN5_avg = 0;
uint32_t ADC_IN6_avg = 0;

uint8_t state_1 = 1;
uint8_t state_2 = 1;


void Get_DATA()
{
	// 简单优化：直接使用ADC原始数值，避免电压转换
	uint32_t tolerance = 50;  // ADC数值容差（约50mV对应的ADC值）

	// 动态阈值：取三个传感器平均值的80%
	uint32_t avg_adc = (ADC_IN4_avg + ADC_IN5_avg + ADC_IN6_avg) / 3;
	uint32_t base_threshold = (avg_adc * 4) / 5;  // 80% = 4/5

	// 左侧阈值
	uint32_t diff_left = (ADC_IN5_avg > ADC_IN6_avg) ?
	                     (ADC_IN5_avg - ADC_IN6_avg) : (ADC_IN6_avg - ADC_IN5_avg);
	if(diff_left < tolerance)
	{
		LEFT_THREASH = ((float)ADC_IN6_avg * 3.3f) / 4096.0f;
	}
	else
	{
		LEFT_THREASH = ((float)base_threshold * 3.3f) / 4096.0f;
	}

	// 右侧阈值
	uint32_t diff_right = (ADC_IN5_avg > ADC_IN4_avg) ?
	                      (ADC_IN5_avg - ADC_IN4_avg) : (ADC_IN4_avg - ADC_IN5_avg);
	if(diff_right < tolerance)
	{
		RIGHT_THREASH = ((float)ADC_IN4_avg * 3.3f) / 4096.0f;
	}
	else
	{
		RIGHT_THREASH = ((float)base_threshold * 3.3f) / 4096.0f;
	}
}

float GetTrace_Date(void)
{
	// 简单优化的循迹算法
	Get_DATA();  // 更新阈值

	// 判断传感器状态（1=在黑线上，0=在白色区域）
	uint8_t left_on_line = (voltage_IN6 < LEFT_THREASH) ? 1 : 0;
	uint8_t center_on_line = (voltage_IN5 < (LEFT_THREASH + RIGHT_THREASH) / 2.0f) ? 1 : 0;
	uint8_t right_on_line = (voltage_IN4 < RIGHT_THREASH) ? 1 : 0;

	// 基于传感器状态的简单判断
	if(center_on_line)
	{
		// 中传感器在线上，正常跟踪
		Data_Out = (voltage_IN6 - voltage_IN4) * 60.0f;
	}
	else if(left_on_line && !right_on_line)
	{
		// 左偏，需要向左转
		Data_Out = -100.0f;
	}
	else if(!left_on_line && right_on_line)
	{
		// 右偏，需要向右转
		Data_Out = 100.0f;
	}
	else if(left_on_line && right_on_line)
	{
		// 宽线或交叉，使用差值
		Data_Out = (voltage_IN6 - voltage_IN4) * 40.0f;
	}
	else
	{
		// 可能丢线，保持原有逻辑
		Data_Out = (voltage_IN6 - voltage_IN4);
		if((Data_Out > 0) && (voltage_IN5 < LEFT_THREASH))
		{
			Data_Out = (2 * LEFT_MAX - voltage_IN6) * 2;
		}
		else if((Data_Out < 0) && (voltage_IN5 < RIGHT_THREASH))
		{
			Data_Out = -(2 * RIGHT_MAX - voltage_IN4) * 2;
		}
	}

	// 限制输出范围
	if(Data_Out > 150.0f) Data_Out = 150.0f;
	if(Data_Out < -150.0f) Data_Out = -150.0f;

	return Data_Out;
}


void adc_tim_dma_init(void)
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // ���ð봫���ж�
}

// ��usart_app.c������Ϊextern

void adc_task(void)
{
		// 简单优化：合并循环，提高效率
		uint32_t ADC_IN4_sum = 0;
		uint32_t ADC_IN5_sum = 0;
		uint32_t ADC_IN6_sum = 0;

		for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
		{
				ADC_IN4[i] = adc_val_buffer[i * 3];
				ADC_IN5[i] = adc_val_buffer[i * 3 + 1];
				ADC_IN6[i] = adc_val_buffer[i * 3 + 2];

				// 同时累加
				ADC_IN4_sum += ADC_IN4[i];
				ADC_IN5_sum += ADC_IN5[i];
				ADC_IN6_sum += ADC_IN6[i];
		}

		// 计算平均值
		ADC_IN4_avg = ADC_IN4_sum / (BUFFER_SIZE / 3);
		ADC_IN5_avg = ADC_IN5_sum / (BUFFER_SIZE / 3);
		ADC_IN6_avg = ADC_IN6_sum / (BUFFER_SIZE / 3);

		// 转换为电压值
		voltage_IN4 = ((float)ADC_IN4_avg * 3.3f) / 4096.0f;
		voltage_IN5 = ((float)ADC_IN5_avg * 3.3f) / 4096.0f;
		voltage_IN6 = ((float)ADC_IN6_avg * 3.3f) / 4096.0f;

		// --- ������� ---

		// ��մ��������� (��ѡ��ȡ���ں����߼�)
		// memset(dac_val_buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));

		// ��� ADC DMA �������ͱ�־λ��׼����һ�βɼ�
		// memset(adc_val_buffer, 0, sizeof(uint32_t) * BUFFER_SIZE); // ���ԭʼ���� (�����Ҫ)

		// �������� ADC DMA ������һ�βɼ�
		// ע�⣺�����ʱ������������ ADC �ģ����ܲ���Ҫ�ֶ�ֹͣ/���� DMA
		// ��Ҫ���� TIM3 �� ADC �ľ������þ����Ƿ���Ҫ��������
}

// 简单的调试函数：获取传感器状态
uint8_t Get_Sensor_Status(void)
{
	Get_DATA();  // 更新阈值

	uint8_t left_on_line = (voltage_IN6 < LEFT_THREASH) ? 1 : 0;
	uint8_t center_on_line = (voltage_IN5 < (LEFT_THREASH + RIGHT_THREASH) / 2.0f) ? 1 : 0;
	uint8_t right_on_line = (voltage_IN4 < RIGHT_THREASH) ? 1 : 0;

	// 返回3位状态：左中右 (例如：101表示左右在线，中间不在线)
	return (left_on_line << 2) | (center_on_line << 1) | right_on_line;
}

