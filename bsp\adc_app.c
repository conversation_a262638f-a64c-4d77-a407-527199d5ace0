
#include "adc_app.h"
#include "stm32f1xx_hal_tim.h"

// 1 ��ѯ
// 2 DMA����ת��
// 3 DMA TIM ��ͨ���ɼ�

// --- ���� <PERSON><PERSON> ��ش��� ---


#define BUFFER_SIZE 300  // 确保是3的倍数，300/3=100个样本每通道
extern DMA_HandleTypeDef hdma_adc1;
uint32_t ADC_IN4[BUFFER_SIZE / 3];
uint32_t ADC_IN5[BUFFER_SIZE / 3];
uint32_t ADC_IN6[BUFFER_SIZE / 3];

__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;
uint8_t wave_analysis_flag = 0; // ���η�����־λ
uint8_t wave_query_type = 0;    // ���β�ѯ���ͣ�0=ȫ��, 1=����, 2=Ƶ��, 3=���ֵ
float error;

float Data_Out; //输出误差
float LEFT_THREASH; //左阈值
float RIGHT_THREASH; //右阈值
float LEFT_MAX = 3.3; //左最大值
float RIGHT_MAX = 3.3; //右最大值

// 自适应线宽识别相关变量
float white_level = 3.0f;      // 白色背景电压值
float black_level = 0.5f;      // 黑线电压值
float threshold_ratio = 0.6f;  // 阈值比例 (0.5-0.7之间)
uint8_t line_width_type = 0;   // 线宽类型：0=未知, 1=窄线, 2=中等, 3=宽线
float adaptive_threshold = 1.5f; // 自适应阈值

// 线宽检测状态
typedef enum {
    LINE_DETECT_INIT = 0,       // 初始化状态
    LINE_DETECT_CALIBRATING,    // 标定中
    LINE_DETECT_TRACKING,       // 循迹中
    LINE_DETECT_LOST           // 丢线状态
} LineDetectState_t;

LineDetectState_t line_detect_state = LINE_DETECT_INIT;
float voltage_IN6; //左传感器的AD值
float voltage_IN5; //中传感器的AD值
float voltage_IN4; //右传感器的AD值

// 添加平均值变量声明
uint32_t ADC_IN4_avg;
uint32_t ADC_IN5_avg;
uint32_t ADC_IN6_avg;

// 自动标定白色和黑色电压值
void Auto_Calibrate_Levels(void)
{
	static uint32_t calib_counter = 0;
	static float white_sum = 0, black_sum = 0;
	static uint8_t white_samples = 0, black_samples = 0;

	calib_counter++;

	// 前100次采样用于标定白色背景
	if(calib_counter <= 100)
	{
		// 假设开始时传感器在白色背景上
		white_sum += (voltage_IN4 + voltage_IN5 + voltage_IN6) / 3.0f;
		white_samples++;
		white_level = white_sum / white_samples;
	}
	// 检测黑线电压值
	else
	{
		float min_voltage = voltage_IN4;
		if(voltage_IN5 < min_voltage) min_voltage = voltage_IN5;
		if(voltage_IN6 < min_voltage) min_voltage = voltage_IN6;

		// 如果检测到明显低于白色背景的电压，认为是黑线
		if(min_voltage < white_level * 0.7f)
		{
			black_sum += min_voltage;
			black_samples++;
			if(black_samples > 0)
			{
				black_level = black_sum / black_samples;
			}
		}
	}

	// 计算自适应阈值
	adaptive_threshold = white_level * threshold_ratio + black_level * (1.0f - threshold_ratio);
}

// 检测线宽类型
void Detect_Line_Width(void)
{
	uint8_t sensors_on_line = 0;

	// 统计有多少传感器检测到黑线
	if(voltage_IN4 < adaptive_threshold) sensors_on_line++;
	if(voltage_IN5 < adaptive_threshold) sensors_on_line++;
	if(voltage_IN6 < adaptive_threshold) sensors_on_line++;

	// 根据传感器数量判断线宽
	switch(sensors_on_line)
	{
		case 0:
			line_width_type = 0; // 无线或丢线
			break;
		case 1:
			line_width_type = 1; // 窄线
			break;
		case 2:
			line_width_type = 2; // 中等线宽
			break;
		case 3:
			line_width_type = 3; // 宽线
			break;
	}
}

// 改进的线宽识别函数
void Get_DATA()
{
	// 自动标定电压值
	Auto_Calibrate_Levels();

	// 检测线宽类型
	Detect_Line_Width();

	// 根据线宽类型设置不同的阈值
	switch(line_width_type)
	{
		case 1: // 窄线
			LEFT_THREASH = adaptive_threshold * 0.9f;
			RIGHT_THREASH = adaptive_threshold * 0.9f;
			break;
		case 2: // 中等线宽
			LEFT_THREASH = adaptive_threshold;
			RIGHT_THREASH = adaptive_threshold;
			break;
		case 3: // 宽线
			LEFT_THREASH = adaptive_threshold * 1.1f;
			RIGHT_THREASH = adaptive_threshold * 1.1f;
			break;
		default: // 未知或无线
			LEFT_THREASH = adaptive_threshold;
			RIGHT_THREASH = adaptive_threshold;
			break;
	}
}


// 自适应循迹误差计算
float GetTrace_Date(void)
{
	float left_sensor = voltage_IN6;   // 左传感器
	float center_sensor = voltage_IN5; // 中传感器
	float right_sensor = voltage_IN4;  // 右传感器

	// 计算传感器与阈值的差值（归一化）
	float left_diff = (adaptive_threshold - left_sensor) / (white_level - black_level);
	float center_diff = (adaptive_threshold - center_sensor) / (white_level - black_level);
	float right_diff = (adaptive_threshold - right_sensor) / (white_level - black_level);

	// 限制差值范围
	if(left_diff > 1.0f) left_diff = 1.0f;
	if(left_diff < -1.0f) left_diff = -1.0f;
	if(center_diff > 1.0f) center_diff = 1.0f;
	if(center_diff < -1.0f) center_diff = -1.0f;
	if(right_diff > 1.0f) right_diff = 1.0f;
	if(right_diff < -1.0f) right_diff = -1.0f;

	// 根据线宽类型采用不同的误差计算方法
	switch(line_width_type)
	{
		case 1: // 窄线：使用加权位置计算
		{
			float position = 0;
			float total_weight = 0;

			// 左传感器权重
			if(left_sensor < adaptive_threshold)
			{
				position += -1.0f * left_diff;
				total_weight += left_diff;
			}
			// 中传感器权重
			if(center_sensor < adaptive_threshold)
			{
				position += 0.0f * center_diff;
				total_weight += center_diff;
			}
			// 右传感器权重
			if(right_sensor < adaptive_threshold)
			{
				position += 1.0f * right_diff;
				total_weight += right_diff;
			}

			if(total_weight > 0.1f)
				Data_Out = position / total_weight * 100.0f;
			else
				Data_Out = 0; // 丢线
			break;
		}

		case 2: // 中等线宽：使用边缘检测
		{
			Data_Out = (left_diff - right_diff) * 50.0f;
			break;
		}

		case 3: // 宽线：使用中心传感器主导
		{
			if(center_sensor < adaptive_threshold)
			{
				// 在线上，使用左右传感器差值
				Data_Out = (left_diff - right_diff) * 30.0f;
			}
			else
			{
				// 偏离中心，使用强化的边缘信号
				Data_Out = (left_diff - right_diff) * 80.0f;
			}
			break;
		}

		default: // 未知线宽：使用简单差值
		{
			Data_Out = (left_sensor - right_sensor) * 50.0f;
			break;
		}
	}

	return Data_Out;
}


void adc_tim_dma_init(void)
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // ���ð봫���ж�
}

// ��usart_app.c������Ϊextern

void adc_task(void)
{
	// DMA循环模式：数据会持续更新到adc_val_buffer
	// 每次调用adc_task()时直接处理当前缓冲区的数据

	// --- 数据采集和处理 ---
	// 分离三个通道的数据到各自数组
	for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
	{
		ADC_IN4[i] = adc_val_buffer[i * 3 + 0];     // 第1个通道 (PA4)
		ADC_IN5[i] = adc_val_buffer[i * 3 + 1];     // 第2个通道 (PA5)
		ADC_IN6[i] = adc_val_buffer[i * 3 + 2];     // 第3个通道 (PA6)
	}
	// 计算每个通道的累加和
	uint32_t ADC_IN4_sum = 0;
	uint32_t ADC_IN5_sum = 0;
	uint32_t ADC_IN6_sum = 0;

	for (uint16_t i = 0; i < BUFFER_SIZE / 3; i++)
	{
		ADC_IN4_sum += ADC_IN4[i];
		ADC_IN5_sum += ADC_IN5[i];
		ADC_IN6_sum += ADC_IN6[i];
	}

	// 计算平均值
	voltage_IN4 = ADC_IN4_sum / (BUFFER_SIZE / 3);
	voltage_IN5 = ADC_IN5_sum / (BUFFER_SIZE / 3);
	voltage_IN6 = ADC_IN6_sum / (BUFFER_SIZE / 3);

	// 转换为电压值 (12位ADC: 0-4095对应0-3.3V)
//	voltage_IN4 = ((float)ADC_IN4_avg * 3.3f) / 4096.0f;
//	voltage_IN5 = ((float)ADC_IN5_avg * 3.3f) / 4096.0f;
//	voltage_IN6 = ((float)ADC_IN6_avg * 3.3f) / 4096.0f;

	// 注意：由于DMA配置为循环模式(DMA_CIRCULAR)，
	// DMA会自动重复采集，不需要手动重启
			// ע�⣺�����ʱ������������ ADC �ģ����ܲ���Ҫ�ֶ�ֹͣ/���� DMA
			// ��Ҫ���� TIM3 �� ADC �ľ������þ����Ƿ���Ҫ��������
}

// 获取当前线宽类型
uint8_t Get_Line_Width_Type(void)
{
	return line_width_type;
}

// 获取自适应阈值
float Get_Adaptive_Threshold(void)
{
	return adaptive_threshold;
}

// 获取标定的电压值
void Get_Calibrated_Levels(float *white, float *black)
{
	*white = white_level;
	*black = black_level;
}

// 获取线宽检测调试信息
void Get_Line_Width_Debug_Info(uint8_t *width_type, float *threshold, float *white, float *black)
{
	*width_type = line_width_type;
	*threshold = adaptive_threshold;
	*white = white_level;
	*black = black_level;
}

// 手动设置阈值比例（用于调试）
void Set_Threshold_Ratio(float ratio)
{
	if(ratio >= 0.3f && ratio <= 0.8f)
	{
		threshold_ratio = ratio;
		adaptive_threshold = white_level * threshold_ratio + black_level * (1.0f - threshold_ratio);
	}
}

// 重置线宽标定
void Reset_Line_Width_Calibration_New(void)
{
	white_level = 3.0f;
	black_level = 0.5f;
	threshold_ratio = 0.6f;
	line_width_type = 0;
	adaptive_threshold = 1.5f;
	line_detect_state = LINE_DETECT_INIT;
}

#endif
