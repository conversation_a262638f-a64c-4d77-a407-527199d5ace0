#include "PID.h"
int16_t error_last = 0;
int16_t PID_num;
uint16_t kp,kd;
typedef struct PID_varity
{
	float P;
	float I;
	float D;
}PID_varity;

int16_t PID_Init()
{
	kp = 100;  // 降低比例系数，避免过度反应
	kd = 0;   // 添加微分项，提高稳定性

	PID_varity PID;
	PID.P = error;
	PID.D = error - error_last;

	error_last = error;

	PID_num = kp*PID.P + kd*PID.D;

	// 限制PID输出范围，避免过大的调节量
	if(PID_num > 300) PID_num = 300;
	else if(PID_num < -300) PID_num = -300;

	return PID_num;
}
