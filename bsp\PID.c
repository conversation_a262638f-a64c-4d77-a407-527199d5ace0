#include "PID.h"
float error_last = 0.0f;  // 改为float类型，与GetTrace_Date()返回类型一致
int16_t PID_num;
float kp,kd;
typedef struct PID_varity
{
	float P;
	float I;
	float D;
}PID_varity;

int16_t PID_Init()
{
	kp = 0.3;   // 降低比例系数，避免数值过大
	kd = 0;    // 添加微分项，提高稳定性

	PID_varity PID;
	float current_error = GetTrace_Date();  // 获取当前误差

	PID.P = current_error;                  // 比例项
	PID.D = current_error - error_last;     // 微分项

	error_last = current_error;             // 更新上次误差

	// 计算PID输出，注意数据类型转换
	float pid_float = (float)kp * PID.P + (float)kd * PID.D;
	PID_num = (int16_t)pid_float;

	// 限制PID输出范围，避免过大的调节量
	if(PID_num > 200) PID_num = 200;
	else if(PID_num < -200) PID_num = -200;

	return PID_num;
}

