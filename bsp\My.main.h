#ifndef __MY_MAIN_H_
#define __MY_MAIN_H_

#include "mydefine.h"
extern float voltage_IN6;
extern float voltage_IN5;
extern float voltage_IN4;
extern uint32_t ADC_IN4_avg;
extern uint32_t ADC_IN5_avg;
extern uint32_t ADC_IN6_avg;
// 电机控制函数声明
void Motor_Init(void);
void Motor_Stop(void);
void Motor_forword(uint16_t speed_l, uint16_t speed_r);
void Motor_back(uint16_t speed_l, uint16_t speed_r);
void Motor_forword_l(int16_t speed);
void Motor_forword_R(int16_t speed);

// 主程序函数声明
void setup(void);
void loop(void);

#endif
